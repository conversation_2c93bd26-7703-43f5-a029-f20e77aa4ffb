"use client";

import { User } from "better-auth";
import React from "react";
import { io, Socket } from "socket.io-client";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import { Textarea } from "../ui/textarea";

let socket: Socket | null = null;

export default ({
  me,
  you,
  chatOpen,
}: {
  me: Partial<User>;
  you: Omit<User, "image" | "email" | "emailVerified" | "createdAt" | "updatedAt" | "name">;
  chatOpen: boolean;
}) => {
  const [message, setMessage] = React.useState("");
  const [chatLog, setChatLog] = React.useState<string[]>([]);

  React.useEffect(() => {
    if (!chatOpen || !me?.id) return;

    if (!socket) {
      socket = io(process.env.NEXT_PUBLIC_MESSAGE_SOCKET_URL!, {
        withCredentials: true,
      });

      // tell server who this user is
      socket.on("connect", () => {
        socket?.emit("register", { userId: me.id });
      });

      socket.on("directMessage", (msg) => {
        setChatLog((prev) => [...prev, `${msg.sender}: ${msg.message}`]);
      });
    }

    return () => {
      socket?.disconnect();
      socket = null;
    };
  }, [chatOpen, me?.id]);

  if (!chatOpen || !me || !you) return null;

  const sendMessage = () => {
    if (!message.trim() || !socket) return;

    socket.emit("directMessage", {
      from: me.id,
      to: you.id,
      message,
    });

    setChatLog((prev) => [...prev, `Me: ${message}`]);
    setMessage("");
  };

  return (
    <div className="w-full flex flex-col gap-2">
      <Textarea value={chatLog.join("\n")} disabled className="h-40 resize-none" />
      <div className="w-full flex flex-row gap-2">
        <Input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter") sendMessage();
          }}
        />
        <Button onClick={sendMessage}>Send</Button>
      </div>
    </div>
  );
};
