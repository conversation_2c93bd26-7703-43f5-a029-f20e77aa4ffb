import "dotenv/config";
import { Server } from "socket.io";
import http from "http";

const httpServer = http.createServer();
const port = Number(process.env.NEXT_PUBLIC_MESSAGE_SOCKET_PORT);

console.log("Socket.IO server starting on port:", port);
console.log("CORS origin:", process.env.NEXT_PUBLIC_HOST_URL);

const io = new Server(httpServer, {
  cors: {
    origin: process.env.NEXT_PUBLIC_HOST_URL,
    methods: ["GET", "POST"],
    credentials: true,
    allowedHeaders: ["*"],
  },
});

const userSocketMap = new Map<string, string>();

io.on("connection", (socket) => {
  console.log("New client:", socket.id);

  // When client connects, it should tell us who they are
  socket.on("register", ({ userId }) => {
    userSocketMap.set(userId, socket.id);
    console.log(`Registered user ${userId} -> ${socket.id}`);
  });

  socket.on("directMessage", ({ to, message, from }) => {
    const targetSocketId = userSocketMap.get(to);

    if (targetSocketId) {
      io.to(targetSocketId).emit("directMessage", {
        sender: from,
        message,
      });
    }
  });

  socket.on("disconnect", () => {
    for (const [userId, sockId] of userSocketMap.entries()) {
      if (sockId === socket.id) {
        userSocketMap.delete(userId);
        console.log(`User ${userId} disconnected`);
        break;
      }
    }
  });
});


httpServer.listen(port, () => {
  console.log(`Socket.IO running on :${port}`);
});
